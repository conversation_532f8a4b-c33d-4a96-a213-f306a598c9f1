/*
 * Copyright (c) 2020, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef ti_dl_dl_driverlib__include
#define ti_dl_dl_driverlib__include

#include <dl_adc12.h>
#include <dl_aes.h>
#include <dl_aesadv.h>
#include <dl_common.h>
#include <dl_comp.h>
#include <dl_crc.h>
#include <dl_crcp.h>
#include <dl_dac12.h>
#include <dl_dma.h>
#include <dl_flashctl.h>
#include <dl_gpamp.h>
#include <dl_gpio.h>
#include <dl_i2c.h>
#include <dl_iwdt.h>
#include <dl_keystorectl.h>
#include <dl_lcd.h>
#include <dl_lfss.h>
#include <dl_mathacl.h>
#include <dl_mcan.h>
#include <dl_opa.h>
#include <dl_rtc.h>
#include <dl_rtc_a.h>
#include <dl_rtc_common.h>
#include <dl_scratchpad.h>
#include <dl_spi.h>
#include <dl_tamperio.h>
#include <dl_timera.h>
#include <dl_timerg.h>
#include <dl_trng.h>
#include <dl_uart_extend.h>
#include <dl_uart_main.h>
#include <dl_vref.h>
#include <dl_wwdt.h>
#include <dl_factoryregion.h>
#include <dl_interrupt.h>
#include <dl_sysctl.h>
#include <dl_systick.h>

#endif /* ti_dl_dl_driverlib__include */
