

#include "ti_msp_dl_config.h"
#include "key.h"
#include "led.h"


unsigned int KEYU_Times=0;
unsigned int KEYD_Times=0;
unsigned int KEYR_Times=0;
unsigned int KEYL_Times=0;
unsigned int KEYM_Times=0;


unsigned char KEYU_Flag=0;
unsigned char KEYD_Flag=0;
unsigned char KEYR_Flag=0;
unsigned char KEYL_Flag=0;
unsigned char KEYM_Flag=0;

void Check_Key(void)
{
	if(KEYU_Flag==0)
 	{	
		if(KEYU==0)
		{	KEYU_Times++;
			if(KEYU_Times>1000){KEYU_Times=0;KEYU_Flag=2;}//??????????	
		}
		else 	
		{	if(KEYU_Times>50)KEYU_Flag=1;//短按处理，统一消抖时间
			if(KEYU_Times>1000)KEYU_Flag=2;//长按处理
			KEYU_Times=0;
		}
	}
  else
 	{	
		if(KEYU!=0)
		{	
			KEYU_Times++;
			if(KEYU_Times>50){KEYU_Flag=0;KEYU_Times=0;}	// 释放消抖时间统一
		}
		else	KEYU_Times=0;
	}
////////////////////////////////////////////////////////////////////////////	
	if(KEYD_Flag==0)
 	{	if(KEYD==0)
		{	KEYD_Times++;
			if(KEYD_Times>1000){KEYD_Times=0;KEYD_Flag=2;}//??????????	
		}
		else 	
		{	if(KEYD_Times>30)KEYD_Flag=1;//?????????
			if(KEYD_Times>1000)KEYD_Flag=2;//??????????
			KEYD_Times=0;
		} 
	}  	
  else
 	{	if(KEYD!=0)
		{	KEYD_Times++;
			if(KEYD_Times>30){KEYD_Flag=0;KEYD_Times=0;}	
		}
		else	KEYD_Times=0;
	}
	if(KEYR_Flag==0)
 	{	if(KEYR==0)
		{	KEYR_Times++;
			if(KEYR_Times>500){KEYR_Times=0;KEYR_Flag=2;}//??????????	
		}
		else 	
		{	if(KEYR_Times>30)KEYR_Flag=1;//?????????
			if(KEYR_Times>500)KEYR_Flag=2;//??????????
			KEYR_Times=0;
		}
	}
  else
 	{	if(KEYR!=0)
		{	KEYR_Times++;
			if(KEYR_Times>30){KEYR_Flag=0;KEYR_Times=0;}	
		}
		else	KEYR_Times=0;
	}	
	if(KEYL_Flag==0)
 	{	if(KEYL==0)
		{	KEYL_Times++;
			if(KEYL_Times>500){KEYL_Times=0;KEYL_Flag=2;}//??????????	
		}
		else 	
		{	if(KEYL_Times>30)KEYL_Flag=1;//?????????
			if(KEYL_Times>500)KEYL_Flag=2;//??????????
			KEYL_Times=0;
		}
	}
  else
 	{	if(KEYL!=0)
		{	KEYL_Times++;
			if(KEYL_Times>30){KEYL_Flag=0;KEYL_Times=0;}	
		}
		else	KEYL_Times=0;	
	}
	if(KEYM_Flag==0)
	{	if(KEYM==0)
		{	KEYM_Times++;
			if(KEYM_Times>500){KEYM_Times=0;KEYM_Flag=2;}//��������
		}
		else
		{	if(KEYM_Times>50)KEYM_Flag=1;//�̰���������������ʱ�䵽50ms
			if(KEYM_Times>500)KEYM_Flag=2;//��������
			KEYM_Times=0;
		}
	}
  else
	{	if(KEYM!=0)
			{	KEYM_Times++;
				if(KEYM_Times>50){KEYM_Flag=0;KEYM_Times=0;}	// 释放消抖时间也增加到50ms
			}
			else	KEYM_Times=0;
	}
}

















