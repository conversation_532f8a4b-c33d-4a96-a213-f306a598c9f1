#ifndef __LED_H_
#define __LED_H_


#include "ti_msp_dl_config.h"


#define LED0_ON 	DL_GPIO_clearPins(LED_L0_PORT, LED_L0_PIN)
#define LED0_OFF 	DL_GPIO_setPins(  LED_L0_PORT, LED_L0_PIN)
#define LED1_ON 	DL_GPIO_clearPins(LED_L1_PORT, LED_L1_PIN)
#define LED1_OFF 	DL_GPIO_setPins(  LED_L1_PORT, LED_L1_PIN)
#define LED2_ON		DL_GPIO_clearPins(LED_L2_PORT, LED_L2_PIN)
#define LED2_OFF 	DL_GPIO_setPins(  LED_L2_PORT, LED_L2_PIN)

void LED_Test(void);

#endif

