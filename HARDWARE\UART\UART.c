

#include "ti_msp_dl_config.h"
#include "uart.h"



uint8_t txData = 0, rxData = 0;


void UART_Test(void)
{
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, 'M');
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, 'S');
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, 'P');
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, 'M');
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, '0');
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, ' ');
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, 'U');
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, 'A');
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, 'R');
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, 'T');
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, ' ');
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, 'T');
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, 'e');
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, 's');
	DL_UART_Main_transmitDataBlocking(HC05_UART_INST, 'T');	
}


void HC05_UART_INST_IRQHandler(void)
{
    switch (DL_UART_Main_getPendingInterrupt(HC05_UART_INST)) 
		{
        case DL_UART_MAIN_IIDX_RX:
            rxData       = DL_UART_Main_receiveDataBlocking(HC05_UART_INST);
            DL_UART_Main_transmitData(HC05_UART_INST, rxData);
            break;
        default:
            break;
    }
}




