
#include "ti_msp_dl_config.h"
#include "delay.h"
#include "timer.h"
#include "LED.h"

unsigned short int PWMx=0;

void Timer_Start(void)
{
	NVIC_EnableIRQ(SYS_TIMER_INST_INT_IRQN);
	DL_TimerG_startCounter(SYS_TIMER_INST);
	DL_TimerA_startCounter(MOTOR_PWM_INST);
	DL_TimerG_startCounter(SERVO1_PWM_INST );
	DL_TimerG_startCounter(SERVO2_PWM_INST );


}

void Timer_PWM_Test(void)
{
		DL_TimerA_setCaptureCompareValue(MOTOR_PWM_INST, PWMx, DL_TIMER_CC_0_INDEX);		
		DL_TimerA_setCaptureCompareValue(MOTOR_PWM_INST, 1600-PWMx, DL_TIMER_CC_1_INDEX);
		DL_TimerG_setCaptureCompareValue(SERVO1_PWM_INST, PWMx, DL_TIMER_CC_0_INDEX);
		DL_TimerG_setCaptureCompareValue(SERVO1_PWM_INST, 1600-PWMx, DL_TIMER_CC_1_INDEX);
		delay_ms(2);	
		PWMx+=1;
		if(PWMx==1600)	PWMx=0;	
}













