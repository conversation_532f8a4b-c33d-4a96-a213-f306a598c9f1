#ifndef _MOTOR_H
#define _MOTOR_H



//#define MOTOR_DIR_M1_DIR1_PORT                                           (GPIOB)
//#define MOTOR_DIR_M1_DIR1_PIN                                   (DL_GPIO_PIN_27)
//#define MOTOR_DIR_M1_DIR1_IOMUX                                  (IOMUX_PINCM58)
///* Defines for M1_DIR2: GPIOB.14 with pinCMx 31 on package pin 2 */
//#define MOTOR_DIR_M1_DIR2_PORT                                           (GPIOB)
//#define MOTOR_DIR_M1_DIR2_PIN                                   (DL_GPIO_PIN_14)
//#define MOTOR_DIR_M1_DIR2_IOMUX                                  (IOMUX_PINCM31)
///* Defines for M2_DIR1: GPIOA.7 with pinCMx 14 on package pin 49 */
//#define MOTOR_DIR_M2_DIR1_PORT                                           (GPIOA)
//#define MOTOR_DIR_M2_DIR1_PIN                                    (DL_GPIO_PIN_7)
//#define MOTOR_DIR_M2_DIR1_IOMUX                                  (IOMUX_PINCM14)
///* Defines for M2_DIR2: GPIOB.19 with pinCMx 45 on package pin 16 */
//#define MOTOR_DIR_M2_DIR2_PORT                                           (GPIOB)
//#define MOTOR_DIR_M2_DIR2_PIN                                   (DL_GPIO_PIN_19)
//#define MOTOR_DIR_M2_DIR2_IOMUX                                  (IOMUX_PINCM45)

#define M1_DIR1_0 	DL_GPIO_clearPins(MOTOR_DIR_M1_DIR1_PORT, MOTOR_DIR_M1_DIR1_PIN)
#define M1_DIR1_1 	DL_GPIO_setPins(  MOTOR_DIR_M1_DIR1_PORT, MOTOR_DIR_M1_DIR1_PIN)
#define M1_DIR2_0 	DL_GPIO_clearPins(MOTOR_DIR_M1_DIR2_PORT, MOTOR_DIR_M1_DIR2_PIN)
#define M1_DIR2_1 	DL_GPIO_setPins(  MOTOR_DIR_M1_DIR2_PORT, MOTOR_DIR_M1_DIR2_PIN)

#define M2_DIR1_0 	DL_GPIO_clearPins(MOTOR_DIR_M2_DIR1_PORT, MOTOR_DIR_M2_DIR1_PIN)
#define M2_DIR1_1	DL_GPIO_setPins(  MOTOR_DIR_M2_DIR1_PORT, MOTOR_DIR_M2_DIR1_PIN)
#define M2_DIR2_0	DL_GPIO_clearPins(MOTOR_DIR_M2_DIR2_PORT, MOTOR_DIR_M2_DIR2_PIN)
#define M2_DIR2_1	DL_GPIO_setPins(  MOTOR_DIR_M2_DIR2_PORT, MOTOR_DIR_M2_DIR2_PIN)

//sbit Motor_R0=P3^4;
//sbit Motor_R1=P3^5;
//sbit Motor_L0=P3^7;
//sbit Motor_L1=P3^6;
/*
sbit Motor_R0=P3^5;
sbit Motor_R1=P3^4;
sbit Motor_L0=P3^6;
sbit Motor_L1=P3^7;
*/
void Motor_Init(void);
void Set_PWM(unsigned char pwm0,unsigned char pwm1);
void Set_Motor(int PMW0,int PMW1);

#endif