
#include <ti_msp_dl_config.h>
#include <LED.h>
#include <key.h>
#include <uart.h>
#include <timer.h>
#include <delay.h>
#include <oled.h>
#include <oled_user.h>
#include <User_IR_Sensor.h>
#include "User_Control.h"
#include "counting.h"

unsigned char Mode=0x50; 	//系统工作模式标记
int main(void)
{
	SYSCFG_DL_init();
	Timer_Start();
	OLED_Init();
	ADC_Init();
	ADC_start();
	LED1_OFF;LED2_OFF;
				
	OLEDLCD_Refresh_AllGDRAM();
	while (1) 
	{
		Display_ADC();
		Binarization_Display(10,0,1);
		OLEDLCD_Refresh_AllGDRAM();		
		delay_ms(5);
	if(KEYM_Flag==1) //短按启动
	{
		if(Mode==0x50) //如果当前是停止模式，则启动
		{
			Mode=0xA1;
		}
		else //如果当前是启动模式，则停止
		{
			Mode=0x50;
		}
		KEYM_Flag=0;//重置标志位，允许再次检测按键

	}
		
	}
}

unsigned char LED_Flag=0;
void SYS_TIMER_INST_IRQHandler(void)
{

	switch (DL_TimerG_getPendingInterrupt(SYS_TIMER_INST)) 
	{
		case DL_TIMER_IIDX_ZERO:
			
			if(ADC_Flag==1)	{ DL_ADC12_startConversion(ADC12_0_INST);  ADC_Flag=0;}//开始转换
			if(ADC1_Flag==1){ DL_ADC12_startConversion(ADC12_1_INST);  ADC1_Flag=0;}//开始转换
			
			Control();
			Check_Key();    //检查按键状态
			counting();
			keycontrol();   //处理按键设置目标圈数
			LED_Flag++;
			if(LED_Flag==100)LED0_OFF;
			if(LED_Flag==200){LED0_ON;LED_Flag=0;};
			break;
		default:
			break;
	}
}

