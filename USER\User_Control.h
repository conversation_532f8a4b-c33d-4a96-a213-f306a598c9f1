#ifndef _USER_CONTROL_H_
#define _USER_CONTROL_H_
#include "OLED_User.h"
#include "KEY.h"
#include "LED.h"
#include "Motor.h"
//#include "STC12C5A60S2_EEPROM.h"
#include "User_IR_Sensor.h"
#include "User_Parameter.h"
#include "User_Control.h"

#define LINE_OUT_TIME 1000

extern unsigned int SysTime_Stick;




extern unsigned int Line_Out_timesStik;
extern unsigned char Line_Out_Flag;

void IR_Identification(void);
void Control(void);
#endif
