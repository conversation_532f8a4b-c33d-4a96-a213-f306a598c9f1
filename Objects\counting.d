./objects/counting.o: USER\counting.c USER\User_Control.h \
  HARDWARE\OLED\OLED_User.h SYSTEM\ti_msp_dl_config.h \
  SYSTEM\ti\devices\msp\msp.h SYSTEM\ti\devices\DeviceFamily.h \
  SYSTEM\ti\devices\msp\m0p\mspm0g350x.h \
  SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h \
  SYSTEM\ti\devices\msp\peripherals\hw_adc12.h \
  SYSTEM\ti\devices\msp\peripherals\hw_aes.h \
  SYSTEM\ti\devices\msp\peripherals\hw_comp.h \
  SYSTEM\ti\devices\msp\peripherals\hw_crc.h \
  SYSTEM\ti\devices\msp\peripherals\hw_dac12.h \
  SYSTEM\ti\devices\msp\peripherals\hw_dma.h \
  SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h \
  SYSTEM\ti\devices\msp\peripherals\hw_gpio.h \
  SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h \
  SYSTEM\ti\devices\msp\peripherals\hw_i2c.h \
  SYSTEM\ti\devices\msp\peripherals\hw_iomux.h \
  SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h \
  SYSTEM\ti\devices\msp\peripherals\hw_mcan.h \
  SYSTEM\ti\devices\msp\peripherals\hw_oa.h \
  SYSTEM\ti\devices\msp\peripherals\hw_rtc.h \
  SYSTEM\ti\devices\msp\peripherals\hw_spi.h \
  SYSTEM\ti\devices\msp\peripherals\hw_trng.h \
  SYSTEM\ti\devices\msp\peripherals\hw_uart.h \
  SYSTEM\ti\devices\msp\peripherals\hw_vref.h \
  SYSTEM\ti\devices\msp\peripherals\hw_wuc.h \
  SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h \
  SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h \
  SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h \
  SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h \
  SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h \
  SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h \
  SYSTEM\ti\driverlib\driverlib.h SYSTEM\ti\driverlib\dl_adc12.h \
  SYSTEM\ti\driverlib\dl_common.h \
  SYSTEM\ti\driverlib\m0p\dl_factoryregion.h \
  SYSTEM\ti\driverlib\m0p\dl_core.h SYSTEM\ti\driverlib\dl_aes.h \
  SYSTEM\ti\driverlib\dl_aesadv.h SYSTEM\ti\driverlib\dl_comp.h \
  SYSTEM\ti\driverlib\dl_crc.h SYSTEM\ti\driverlib\dl_crcp.h \
  SYSTEM\ti\driverlib\dl_dac12.h SYSTEM\ti\driverlib\dl_dma.h \
  SYSTEM\ti\driverlib\dl_flashctl.h SYSTEM\ti\driverlib\m0p\dl_sysctl.h \
  SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h \
  SYSTEM\ti\driverlib\dl_gpamp.h SYSTEM\ti\driverlib\dl_gpio.h \
  SYSTEM\ti\driverlib\dl_i2c.h SYSTEM\ti\driverlib\dl_iwdt.h \
  SYSTEM\ti\driverlib\dl_lfss.h SYSTEM\ti\driverlib\dl_keystorectl.h \
  SYSTEM\ti\driverlib\dl_lcd.h SYSTEM\ti\driverlib\dl_mathacl.h \
  SYSTEM\ti\driverlib\dl_mcan.h SYSTEM\ti\driverlib\dl_opa.h \
  SYSTEM\ti\driverlib\dl_rtc.h SYSTEM\ti\driverlib\dl_rtc_common.h \
  SYSTEM\ti\driverlib\dl_rtc_a.h SYSTEM\ti\driverlib\dl_scratchpad.h \
  SYSTEM\ti\driverlib\dl_spi.h SYSTEM\ti\driverlib\dl_tamperio.h \
  SYSTEM\ti\driverlib\dl_timera.h SYSTEM\ti\driverlib\dl_timer.h \
  SYSTEM\ti\driverlib\dl_timerg.h SYSTEM\ti\driverlib\dl_trng.h \
  SYSTEM\ti\driverlib\dl_uart_extend.h SYSTEM\ti\driverlib\dl_uart.h \
  SYSTEM\ti\driverlib\dl_uart_main.h SYSTEM\ti\driverlib\dl_vref.h \
  SYSTEM\ti\driverlib\dl_wwdt.h SYSTEM\ti\driverlib\m0p\dl_interrupt.h \
  SYSTEM\ti\driverlib\m0p\dl_systick.h HARDWARE\OLED\oled.h \
  SYSTEM\delay.h HARDWARE\KEY\KEY.h HARDWARE\LED\LED.h \
  HARDWARE\MOTOR\Motor.h USER\User_IR_Sensor.h USER\User_Parameter.h \
  USER\counting.h
