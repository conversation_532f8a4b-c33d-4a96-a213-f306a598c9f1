/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --package "LQFP-64(PM)" --part "Default" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.20.0+3587"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12    = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121   = ADC12.addInstance();
const CAPTURE  = scripting.addModule("/ti/driverlib/CAPTURE", {}, false);
const CAPTURE1 = CAPTURE.addInstance();
const GPIO     = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1    = GPIO.addInstance();
const GPIO2    = GPIO.addInstance();
const GPIO3    = GPIO.addInstance();
const GPIO4    = GPIO.addInstance();
const GPIO5    = GPIO.addInstance();
const GPIO6    = GPIO.addInstance();
const GPIO7    = GPIO.addInstance();
const GPIO8    = GPIO.addInstance();
const GPIO9    = GPIO.addInstance();
const GPIO10   = GPIO.addInstance();
const I2C      = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1     = I2C.addInstance();
const PWM      = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1     = PWM.addInstance();
const PWM2     = PWM.addInstance();
const PWM3     = PWM.addInstance();
const QEI      = scripting.addModule("/ti/driverlib/QEI", {}, false);
const QEI1     = QEI.addInstance();
const SPI      = scripting.addModule("/ti/driverlib/SPI", {}, false);
const SPI1     = SPI.addInstance();
const SYSCTL   = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER    = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1   = TIMER.addInstance();
const UART     = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1    = UART.addInstance();
const UART2    = UART.addInstance();
const UART3    = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 8;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 40;

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL0";

ADC121.$name                 = "ADC12_0";
ADC121.sampClkSrc            = "DL_ADC12_CLOCK_ULPCLK";
ADC121.sampClkDiv            = "DL_ADC12_CLOCK_DIVIDE_8";
ADC121.samplingOperationMode = "sequence";
ADC121.endAdd                = 8;
ADC121.adcMem0chansel        = "DL_ADC12_INPUT_CHAN_1";
ADC121.adcMem1chansel        = "DL_ADC12_INPUT_CHAN_2";
ADC121.adcMem2chansel        = "DL_ADC12_INPUT_CHAN_3";
ADC121.adcMem3chansel        = "DL_ADC12_INPUT_CHAN_4";
ADC121.adcMem4chansel        = "DL_ADC12_INPUT_CHAN_5";
ADC121.adcMem5chansel        = "DL_ADC12_INPUT_CHAN_6";
ADC121.adcMem6chansel        = "DL_ADC12_INPUT_CHAN_7";
ADC121.adcMem7chansel        = "DL_ADC12_INPUT_CHAN_8";
ADC121.adcMem8chansel        = "DL_ADC12_INPUT_CHAN_12";
ADC121.enabledInterrupts     = ["DL_ADC12_INTERRUPT_MEM8_RESULT_LOADED"];
ADC121.interruptPriority     = "0";
ADC121.resolution            = "DL_ADC12_SAMP_CONV_RES_8_BIT";
ADC121.powerDownMode         = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.sampleTime0           = "40us";
ADC121.sampleTime1           = "40us";
ADC121.adcPin1Config.$name   = "ti_driverlib_gpio_GPIOPinGeneric1";
ADC121.adcPin2Config.$name   = "ti_driverlib_gpio_GPIOPinGeneric21";
ADC121.adcPin3Config.$name   = "ti_driverlib_gpio_GPIOPinGeneric22";
ADC121.adcPin4Config.$name   = "ti_driverlib_gpio_GPIOPinGeneric23";
ADC121.adcPin5Config.$name   = "ti_driverlib_gpio_GPIOPinGeneric24";
ADC121.adcPin6Config.$name   = "ti_driverlib_gpio_GPIOPinGeneric25";
ADC121.adcPin7Config.$name   = "ti_driverlib_gpio_GPIOPinGeneric26";
ADC121.adcPin8Config.$name   = "ti_driverlib_gpio_GPIOPinGeneric27";
ADC121.adcPin12Config.$name  = "ti_driverlib_gpio_GPIOPinGeneric0";

const Board           = scripting.addModule("/ti/driverlib/Board", {}, false);
Board.configureUnused = true;

CAPTURE1.$name                      = "CAPTURE_0";
CAPTURE1.peripheral.$assign         = "TIMG7";
CAPTURE1.peripheral.ccp0Pin.$assign = "PA28";
CAPTURE1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric36";

GPIO1.$name                         = "LED";
GPIO1.associatedPins.create(3);
GPIO1.associatedPins[0].$name       = "L0";
GPIO1.associatedPins[0].pin.$assign = "PA0";
GPIO1.associatedPins[1].$name       = "L1";
GPIO1.associatedPins[1].pin.$assign = "PB22";
GPIO1.associatedPins[2].$name       = "L2";
GPIO1.associatedPins[2].pin.$assign = "PB26";

GPIO2.$name                         = "GPIO_S1";
GPIO2.associatedPins[0].$name       = "PIN_3";
GPIO2.associatedPins[0].direction   = "INPUT";
GPIO2.associatedPins[0].pin.$assign = "PA18";

GPIO3.port                          = "PORTB";
GPIO3.$name                         = "OLED";
GPIO3.associatedPins[0].$name       = "RST";
GPIO3.associatedPins[0].pin.$assign = "PB17";

GPIO4.$name                         = "KEY";
GPIO4.associatedPins.create(5);
GPIO4.associatedPins[0].$name       = "KU";
GPIO4.associatedPins[0].direction   = "INPUT";
GPIO4.associatedPins[0].pin.$assign = "PB8";
GPIO4.associatedPins[1].$name       = "KD";
GPIO4.associatedPins[1].direction   = "INPUT";
GPIO4.associatedPins[1].pin.$assign = "PB9";
GPIO4.associatedPins[2].$name       = "KR";
GPIO4.associatedPins[2].direction   = "INPUT";
GPIO4.associatedPins[2].pin.$assign = "PB10";
GPIO4.associatedPins[3].$name       = "KL";
GPIO4.associatedPins[3].direction   = "INPUT";
GPIO4.associatedPins[3].pin.$assign = "PB11";
GPIO4.associatedPins[4].$name       = "KM";
GPIO4.associatedPins[4].direction   = "INPUT";
GPIO4.associatedPins[4].pin.$assign = "PB21";

GPIO5.$name                         = "ULN1";
GPIO5.associatedPins.create(2);
GPIO5.associatedPins[0].$name       = "TIRG1";
GPIO5.associatedPins[0].pin.$assign = "PB12";
GPIO5.associatedPins[1].$name       = "ECHO1";
GPIO5.associatedPins[1].direction   = "INPUT";
GPIO5.associatedPins[1].pin.$assign = "PA31";

GPIO6.$name                         = "ULN2";
GPIO6.associatedPins.create(2);
GPIO6.associatedPins[0].$name       = "TIRG2";
GPIO6.associatedPins[0].pin.$assign = "PB23";
GPIO6.associatedPins[1].$name       = "ECHO2";
GPIO6.associatedPins[1].direction   = "INPUT";
GPIO6.associatedPins[1].pin.$assign = "PB13";

GPIO7.$name                         = "FLASH";
GPIO7.associatedPins[0].$name       = "FCS";
GPIO7.associatedPins[0].pin.$assign = "PA1";

GPIO8.$name                         = "BEEP";
GPIO8.associatedPins[0].$name       = "PIN_0";
GPIO8.associatedPins[0].pin.$assign = "PB0";

GPIO9.$name                         = "ENCODE";
GPIO9.associatedPins[0].$name       = "DIR1";
GPIO9.associatedPins[0].pin.$assign = "PA2";

GPIO10.$name                          = "MOTOR_DIR";
GPIO10.associatedPins.create(4);
GPIO10.associatedPins[0].$name        = "M1_DIR1";
GPIO10.associatedPins[0].assignedPort = "PORTB";
GPIO10.associatedPins[0].pin.$assign  = "PB27";
GPIO10.associatedPins[1].$name        = "M1_DIR2";
GPIO10.associatedPins[1].assignedPort = "PORTB";
GPIO10.associatedPins[2].$name        = "M2_DIR1";
GPIO10.associatedPins[2].assignedPort = "PORTA";
GPIO10.associatedPins[2].pin.$assign  = "PA7";
GPIO10.associatedPins[3].$name        = "M2_DIR2";
GPIO10.associatedPins[3].assignedPort = "PORTB";
GPIO10.associatedPins[3].pin.$assign  = "PB19";

I2C1.$name                     = "I2C_0";
I2C1.basicEnableController     = true;
I2C1.peripheral.sdaPin.$assign = "PB3";
I2C1.peripheral.sclPin.$assign = "PB2";
I2C1.sdaPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric13";
I2C1.sclPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric14";

PWM1.crossTriggerEn             = true;
PWM1.$name                      = "MOTOR_PWM";
PWM1.timerCount                 = 8000;
PWM1.ccIndex                    = [2,3];
PWM1.peripheral.$assign         = "TIMA0";
PWM1.peripheral.ccp2Pin.$assign = "PA3";
PWM1.peripheral.ccp3Pin.$assign = "PA4";
PWM1.PWM_CHANNEL_2.$name        = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_3.$name        = "ti_driverlib_pwm_PWMTimerCC5";
PWM1.ccp2PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric4";
PWM1.ccp3PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric5";

PWM2.crossTriggerEn                     = true;
PWM2.crossTriggerAuthority              = "Secondary";
PWM2.secondaryCrossTriggerSource        = "InputTrigger_1";
PWM2.clockDivider                       = 2;
PWM2.timerCount                         = 20000;
PWM2.clockPrescale                      = 16;
PWM2.$name                              = "SERVO1_PWM";
PWM2.peripheral.$assign                 = "TIMG0";
PWM2.peripheral.ccp0Pin.$assign         = "PA12";
PWM2.peripheral.ccp1Pin.$assign         = "PA13";
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM2.PWM_CHANNEL_0.ccValue              = 1000;
PWM2.PWM_CHANNEL_0.invert               = true;
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM2.PWM_CHANNEL_1.ccValue              = 1500;
PWM2.PWM_CHANNEL_1.invert               = true;
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";

PWM3.$name                              = "SERVO2_PWM";
PWM3.peripheral.ccp0Pin.$assign         = "PB4";
PWM3.peripheral.ccp1Pin.$assign         = "PB5";
PWM3.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC6";
PWM3.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC7";
PWM3.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM3.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM3.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM3.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM3.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric15";
PWM3.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM3.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM3.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM3.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM3.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric16";

QEI1.$name                      = "QEI_0";
QEI1.peripheral.$assign         = "TIMG8";
QEI1.peripheral.ccp0Pin.$assign = "PA29";
QEI1.peripheral.ccp1Pin.$assign = "PA30";
QEI1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric30";
QEI1.ccp1PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric35";

SPI1.targetBitRate                      = 500000;
SPI1.enableCDMode                       = true;
SPI1.$name                              = "LCD_SPI";
SPI1.peripheral.$assign                 = "SPI1";
SPI1.peripheral.sclkPin.$assign         = "PB16";
SPI1.peripheral.mosiPin.$assign         = "PB15";
SPI1.peripheral.misoPin.$assign         = "PB7";
SPI1.peripheral.cs0Pin.$assign          = "PB6";
SPI1.peripheral.cs3Pin.$assign          = "PB1";
SPI1.sclkPinConfig.direction            = scripting.forceWrite("OUTPUT");
SPI1.sclkPinConfig.onlyInternalResistor = scripting.forceWrite(false);
SPI1.sclkPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
SPI1.sclkPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric8";
SPI1.mosiPinConfig.direction            = scripting.forceWrite("OUTPUT");
SPI1.mosiPinConfig.hideOutputInversion  = scripting.forceWrite(false);
SPI1.mosiPinConfig.onlyInternalResistor = scripting.forceWrite(false);
SPI1.mosiPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
SPI1.mosiPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric9";
SPI1.misoPinConfig.onlyInternalResistor = scripting.forceWrite(false);
SPI1.misoPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
SPI1.misoPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric10";
SPI1.cs0PinConfig.direction             = scripting.forceWrite("OUTPUT");
SPI1.cs0PinConfig.hideOutputInversion   = scripting.forceWrite(false);
SPI1.cs0PinConfig.onlyInternalResistor  = scripting.forceWrite(false);
SPI1.cs0PinConfig.passedPeripheralType  = scripting.forceWrite("Digital");
SPI1.cs0PinConfig.$name                 = "ti_driverlib_gpio_GPIOPinGeneric11";
SPI1.cs3PinConfig.direction             = scripting.forceWrite("OUTPUT");
SPI1.cs3PinConfig.hideOutputInversion   = scripting.forceWrite(false);
SPI1.cs3PinConfig.onlyInternalResistor  = scripting.forceWrite(false);
SPI1.cs3PinConfig.passedPeripheralType  = scripting.forceWrite("Digital");
SPI1.cs3PinConfig.$name                 = "ti_driverlib_gpio_GPIOPinGeneric12";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

TIMER1.interrupts          = ["ZERO"];
TIMER1.retentionRestoreCnt = true;
TIMER1.timerMode           = "PERIODIC";
TIMER1.$name               = "SYS_TIMER";
TIMER1.timerPeriod         = "1ms";
TIMER1.peripheral.$assign  = "TIMG12";

UART1.targetBaudRate                   = 115200;
UART1.$name                            = "HC05_UART";
UART1.peripheral.$assign               = "UART0";
UART1.peripheral.rxPin.$assign         = "PA11";
UART1.peripheral.txPin.$assign         = "PA10";
UART1.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART1.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";
UART1.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric7";

UART2.$name                            = "SENSER_UART";
UART2.peripheral.$assign               = "UART1";
UART2.peripheral.rxPin.$assign         = "PA9";
UART2.peripheral.txPin.$assign         = "PA8";
UART2.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART2.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric17";
UART2.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric18";

UART3.$name                            = "SERVO_UART";
UART3.peripheral.$assign               = "UART2";
UART3.peripheral.rxPin.$assign         = "PB18";
UART3.peripheral.txPin.$assign         = "PA23";
UART3.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART3.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART3.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART3.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART3.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric19";
UART3.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART3.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART3.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART3.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric20";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
ADC121.peripheral.$suggestSolution            = "ADC0";
ADC121.peripheral.adcPin1.$suggestSolution    = "PA26";
ADC121.peripheral.adcPin2.$suggestSolution    = "PA25";
ADC121.peripheral.adcPin3.$suggestSolution    = "PA24";
ADC121.peripheral.adcPin4.$suggestSolution    = "PB25";
ADC121.peripheral.adcPin5.$suggestSolution    = "PB24";
ADC121.peripheral.adcPin6.$suggestSolution    = "PB20";
ADC121.peripheral.adcPin7.$suggestSolution    = "PA22";
ADC121.peripheral.adcPin8.$suggestSolution    = "PA21";
ADC121.peripheral.adcPin12.$suggestSolution   = "PA14";
Board.peripheral.$suggestSolution             = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution    = "PA20";
Board.peripheral.swdioPin.$suggestSolution    = "PA19";
GPIO10.associatedPins[1].pin.$suggestSolution = "PB14";
I2C1.peripheral.$suggestSolution              = "I2C1";
PWM3.peripheral.$suggestSolution              = "TIMA1";
