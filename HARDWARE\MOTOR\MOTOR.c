#include "ti_msp_dl_config.h"
#include "motor.h"

void Set_Motor(int PMW0,int PMW1)
{

	if(PMW0<-7999)PMW0=-7999;
	if(PMW0>7999)PMW0=7999;

	if(PMW1<-7999)PMW1=-7999;
	if(PMW1>7999)PMW1=7999;

	if(PMW0<0)
	{
	  	M1_DIR1_1;
	  	M1_DIR2_0;
			PMW0=0-PMW0;
	}
	else if(PMW0==0)
	{
   		M1_DIR1_1;
	  	M1_DIR2_1;
	}
	else
	{
	  	M1_DIR1_0;
	  	M1_DIR2_1;		
	}
///////////////////////////////
	if(PMW1<0)
	{
	  	M2_DIR1_0;
	  	M2_DIR2_1;
			PMW1=0-PMW1;
	}
	else if(PMW1==0)
	{
			M2_DIR1_1;
	  	M2_DIR2_1;
	}
	else
	{
	  	M2_DIR1_1;
	  	M2_DIR2_0;	
	}
	DL_Timer_setCaptureCompareValue(MOTOR_PWM_INST, PMW0, DL_TIMER_CC_2_INDEX);
	DL_Timer_setCaptureCompareValue(MOTOR_PWM_INST,	PMW1, DL_TIMER_CC_3_INDEX);
	
//	Set_PWM(PMW0&0xFF,PMW1&0xFF);

}



















