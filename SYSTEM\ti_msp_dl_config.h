/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)



#define CPUCLK_FREQ                                                     80000000



/* Defines for MOTOR_PWM */
#define MOTOR_PWM_INST                                                     TIMA0
#define MOTOR_PWM_INST_IRQHandler                               TIMA0_IRQHandler
#define MOTOR_PWM_INST_INT_IRQN                                 (TIMA0_INT_IRQn)
#define MOTOR_PWM_INST_CLK_FREQ                                         80000000
/* GPIO defines for channel 2 */
#define GPIO_MOTOR_PWM_C2_PORT                                             GPIOA
#define GPIO_MOTOR_PWM_C2_PIN                                      DL_GPIO_PIN_3
#define GPIO_MOTOR_PWM_C2_IOMUX                                   (IOMUX_PINCM8)
#define GPIO_MOTOR_PWM_C2_IOMUX_FUNC                  IOMUX_PINCM8_PF_TIMA0_CCP2
#define GPIO_MOTOR_PWM_C2_IDX                                DL_TIMER_CC_2_INDEX
/* GPIO defines for channel 3 */
#define GPIO_MOTOR_PWM_C3_PORT                                             GPIOA
#define GPIO_MOTOR_PWM_C3_PIN                                      DL_GPIO_PIN_4
#define GPIO_MOTOR_PWM_C3_IOMUX                                   (IOMUX_PINCM9)
#define GPIO_MOTOR_PWM_C3_IOMUX_FUNC                  IOMUX_PINCM9_PF_TIMA0_CCP3
#define GPIO_MOTOR_PWM_C3_IDX                                DL_TIMER_CC_3_INDEX

/* Defines for SERVO1_PWM */
#define SERVO1_PWM_INST                                                    TIMG0
#define SERVO1_PWM_INST_IRQHandler                              TIMG0_IRQHandler
#define SERVO1_PWM_INST_INT_IRQN                                (TIMG0_INT_IRQn)
#define SERVO1_PWM_INST_CLK_FREQ                                         1250000
/* GPIO defines for channel 0 */
#define GPIO_SERVO1_PWM_C0_PORT                                            GPIOA
#define GPIO_SERVO1_PWM_C0_PIN                                    DL_GPIO_PIN_12
#define GPIO_SERVO1_PWM_C0_IOMUX                                 (IOMUX_PINCM34)
#define GPIO_SERVO1_PWM_C0_IOMUX_FUNC                IOMUX_PINCM34_PF_TIMG0_CCP0
#define GPIO_SERVO1_PWM_C0_IDX                               DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_SERVO1_PWM_C1_PORT                                            GPIOA
#define GPIO_SERVO1_PWM_C1_PIN                                    DL_GPIO_PIN_13
#define GPIO_SERVO1_PWM_C1_IOMUX                                 (IOMUX_PINCM35)
#define GPIO_SERVO1_PWM_C1_IOMUX_FUNC                IOMUX_PINCM35_PF_TIMG0_CCP1
#define GPIO_SERVO1_PWM_C1_IDX                               DL_TIMER_CC_1_INDEX

/* Defines for SERVO2_PWM */
#define SERVO2_PWM_INST                                                    TIMA1
#define SERVO2_PWM_INST_IRQHandler                              TIMA1_IRQHandler
#define SERVO2_PWM_INST_INT_IRQN                                (TIMA1_INT_IRQn)
#define SERVO2_PWM_INST_CLK_FREQ                                        80000000
/* GPIO defines for channel 0 */
#define GPIO_SERVO2_PWM_C0_PORT                                            GPIOB
#define GPIO_SERVO2_PWM_C0_PIN                                     DL_GPIO_PIN_4
#define GPIO_SERVO2_PWM_C0_IOMUX                                 (IOMUX_PINCM17)
#define GPIO_SERVO2_PWM_C0_IOMUX_FUNC                IOMUX_PINCM17_PF_TIMA1_CCP0
#define GPIO_SERVO2_PWM_C0_IDX                               DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_SERVO2_PWM_C1_PORT                                            GPIOB
#define GPIO_SERVO2_PWM_C1_PIN                                     DL_GPIO_PIN_5
#define GPIO_SERVO2_PWM_C1_IOMUX                                 (IOMUX_PINCM18)
#define GPIO_SERVO2_PWM_C1_IOMUX_FUNC                IOMUX_PINCM18_PF_TIMA1_CCP1
#define GPIO_SERVO2_PWM_C1_IDX                               DL_TIMER_CC_1_INDEX




/* Defines for QEI_0 */
#define QEI_0_INST                                                         TIMG8
#define QEI_0_INST_IRQHandler                                   TIMG8_IRQHandler
#define QEI_0_INST_INT_IRQN                                     (TIMG8_INT_IRQn)
/* Pin configuration defines for QEI_0 PHA Pin */
#define GPIO_QEI_0_PHA_PORT                                                GPIOA
#define GPIO_QEI_0_PHA_PIN                                        DL_GPIO_PIN_29
#define GPIO_QEI_0_PHA_IOMUX                                      (IOMUX_PINCM4)
#define GPIO_QEI_0_PHA_IOMUX_FUNC                     IOMUX_PINCM4_PF_TIMG8_CCP0
/* Pin configuration defines for QEI_0 PHB Pin */
#define GPIO_QEI_0_PHB_PORT                                                GPIOA
#define GPIO_QEI_0_PHB_PIN                                        DL_GPIO_PIN_30
#define GPIO_QEI_0_PHB_IOMUX                                      (IOMUX_PINCM5)
#define GPIO_QEI_0_PHB_IOMUX_FUNC                     IOMUX_PINCM5_PF_TIMG8_CCP1


/* Defines for CAPTURE_0 */
#define CAPTURE_0_INST                                                   (TIMG7)
#define CAPTURE_0_INST_IRQHandler                               TIMG7_IRQHandler
#define CAPTURE_0_INST_INT_IRQN                                 (TIMG7_INT_IRQn)
#define CAPTURE_0_INST_LOAD_VALUE                                           (0U)
/* GPIO defines for channel 0 */
#define GPIO_CAPTURE_0_C0_PORT                                             GPIOA
#define GPIO_CAPTURE_0_C0_PIN                                     DL_GPIO_PIN_28
#define GPIO_CAPTURE_0_C0_IOMUX                                   (IOMUX_PINCM3)
#define GPIO_CAPTURE_0_C0_IOMUX_FUNC                  IOMUX_PINCM3_PF_TIMG7_CCP0





/* Defines for SYS_TIMER */
#define SYS_TIMER_INST                                                  (TIMG12)
#define SYS_TIMER_INST_IRQHandler                              TIMG12_IRQHandler
#define SYS_TIMER_INST_INT_IRQN                                (TIMG12_INT_IRQn)
#define SYS_TIMER_INST_LOAD_VALUE                                       (79999U)




/* Defines for I2C_0 */
#define I2C_0_INST                                                          I2C1
#define I2C_0_INST_IRQHandler                                    I2C1_IRQHandler
#define I2C_0_INST_INT_IRQN                                        I2C1_INT_IRQn
#define I2C_0_BUS_SPEED_HZ                                                100000
#define GPIO_I2C_0_SDA_PORT                                                GPIOB
#define GPIO_I2C_0_SDA_PIN                                         DL_GPIO_PIN_3
#define GPIO_I2C_0_IOMUX_SDA                                     (IOMUX_PINCM16)
#define GPIO_I2C_0_IOMUX_SDA_FUNC                      IOMUX_PINCM16_PF_I2C1_SDA
#define GPIO_I2C_0_SCL_PORT                                                GPIOB
#define GPIO_I2C_0_SCL_PIN                                         DL_GPIO_PIN_2
#define GPIO_I2C_0_IOMUX_SCL                                     (IOMUX_PINCM15)
#define GPIO_I2C_0_IOMUX_SCL_FUNC                      IOMUX_PINCM15_PF_I2C1_SCL


/* Defines for HC05_UART */
#define HC05_UART_INST                                                     UART0
#define HC05_UART_INST_IRQHandler                               UART0_IRQHandler
#define HC05_UART_INST_INT_IRQN                                   UART0_INT_IRQn
#define GPIO_HC05_UART_RX_PORT                                             GPIOA
#define GPIO_HC05_UART_TX_PORT                                             GPIOA
#define GPIO_HC05_UART_RX_PIN                                     DL_GPIO_PIN_11
#define GPIO_HC05_UART_TX_PIN                                     DL_GPIO_PIN_10
#define GPIO_HC05_UART_IOMUX_RX                                  (IOMUX_PINCM22)
#define GPIO_HC05_UART_IOMUX_TX                                  (IOMUX_PINCM21)
#define GPIO_HC05_UART_IOMUX_RX_FUNC                   IOMUX_PINCM22_PF_UART0_RX
#define GPIO_HC05_UART_IOMUX_TX_FUNC                   IOMUX_PINCM21_PF_UART0_TX
#define HC05_UART_BAUD_RATE                                             (115200)
#define HC05_UART_IBRD_40_MHZ_115200_BAUD                                   (21)
#define HC05_UART_FBRD_40_MHZ_115200_BAUD                                   (45)
/* Defines for SENSER_UART */
#define SENSER_UART_INST                                                   UART1
#define SENSER_UART_INST_IRQHandler                             UART1_IRQHandler
#define SENSER_UART_INST_INT_IRQN                                 UART1_INT_IRQn
#define GPIO_SENSER_UART_RX_PORT                                           GPIOA
#define GPIO_SENSER_UART_TX_PORT                                           GPIOA
#define GPIO_SENSER_UART_RX_PIN                                    DL_GPIO_PIN_9
#define GPIO_SENSER_UART_TX_PIN                                    DL_GPIO_PIN_8
#define GPIO_SENSER_UART_IOMUX_RX                                (IOMUX_PINCM20)
#define GPIO_SENSER_UART_IOMUX_TX                                (IOMUX_PINCM19)
#define GPIO_SENSER_UART_IOMUX_RX_FUNC                 IOMUX_PINCM20_PF_UART1_RX
#define GPIO_SENSER_UART_IOMUX_TX_FUNC                 IOMUX_PINCM19_PF_UART1_TX
#define SENSER_UART_BAUD_RATE                                             (9600)
#define SENSER_UART_IBRD_40_MHZ_9600_BAUD                                  (260)
#define SENSER_UART_FBRD_40_MHZ_9600_BAUD                                   (27)
/* Defines for SERVO_UART */
#define SERVO_UART_INST                                                    UART2
#define SERVO_UART_INST_IRQHandler                              UART2_IRQHandler
#define SERVO_UART_INST_INT_IRQN                                  UART2_INT_IRQn
#define GPIO_SERVO_UART_RX_PORT                                            GPIOB
#define GPIO_SERVO_UART_TX_PORT                                            GPIOA
#define GPIO_SERVO_UART_RX_PIN                                    DL_GPIO_PIN_18
#define GPIO_SERVO_UART_TX_PIN                                    DL_GPIO_PIN_23
#define GPIO_SERVO_UART_IOMUX_RX                                 (IOMUX_PINCM44)
#define GPIO_SERVO_UART_IOMUX_TX                                 (IOMUX_PINCM53)
#define GPIO_SERVO_UART_IOMUX_RX_FUNC                  IOMUX_PINCM44_PF_UART2_RX
#define GPIO_SERVO_UART_IOMUX_TX_FUNC                  IOMUX_PINCM53_PF_UART2_TX
#define SERVO_UART_BAUD_RATE                                              (9600)
#define SERVO_UART_IBRD_40_MHZ_9600_BAUD                                   (260)
#define SERVO_UART_FBRD_40_MHZ_9600_BAUD                                    (27)




/* Defines for LCD_SPI */
#define LCD_SPI_INST                                                       SPI1
#define LCD_SPI_INST_IRQHandler                                 SPI1_IRQHandler
#define LCD_SPI_INST_INT_IRQN                                     SPI1_INT_IRQn
#define GPIO_LCD_SPI_PICO_PORT                                            GPIOB
#define GPIO_LCD_SPI_PICO_PIN                                    DL_GPIO_PIN_15
#define GPIO_LCD_SPI_IOMUX_PICO                                 (IOMUX_PINCM32)
#define GPIO_LCD_SPI_IOMUX_PICO_FUNC                 IOMUX_PINCM32_PF_SPI1_PICO
#define GPIO_LCD_SPI_POCI_PORT                                            GPIOB
#define GPIO_LCD_SPI_POCI_PIN                                     DL_GPIO_PIN_7
#define GPIO_LCD_SPI_IOMUX_POCI                                 (IOMUX_PINCM24)
#define GPIO_LCD_SPI_IOMUX_POCI_FUNC                 IOMUX_PINCM24_PF_SPI1_POCI
/* GPIO configuration for LCD_SPI */
#define GPIO_LCD_SPI_SCLK_PORT                                            GPIOB
#define GPIO_LCD_SPI_SCLK_PIN                                    DL_GPIO_PIN_16
#define GPIO_LCD_SPI_IOMUX_SCLK                                 (IOMUX_PINCM33)
#define GPIO_LCD_SPI_IOMUX_SCLK_FUNC                 IOMUX_PINCM33_PF_SPI1_SCLK
#define GPIO_LCD_SPI_CS0_PORT                                             GPIOB
#define GPIO_LCD_SPI_CS0_PIN                                      DL_GPIO_PIN_6
#define GPIO_LCD_SPI_IOMUX_CS0                                  (IOMUX_PINCM23)
#define GPIO_LCD_SPI_IOMUX_CS0_FUNC                   IOMUX_PINCM23_PF_SPI1_CS0
#define GPIO_LCD_SPI_CD_PORT                                              GPIOB
#define GPIO_LCD_SPI_CD_PIN                                       DL_GPIO_PIN_1
#define GPIO_LCD_SPI_IOMUX_CD                                   (IOMUX_PINCM13)
#define GPIO_LCD_SPI_IOMUX_CD_FUNC           IOMUX_PINCM13_PF_SPI1_CS3_CD_POCI3



/* Defines for ADC12_0 */
#define ADC12_0_INST                                                        ADC0
#define ADC12_0_INST_IRQHandler                                  ADC0_IRQHandler
#define ADC12_0_INST_INT_IRQN                                    (ADC0_INT_IRQn)
#define ADC12_0_ADCMEM_0                                      DL_ADC12_MEM_IDX_0
#define ADC12_0_ADCMEM_0_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_0_ADCMEM_0_REF_VOLTAGE_V                                       3.3
#define ADC12_0_ADCMEM_1                                      DL_ADC12_MEM_IDX_1
#define ADC12_0_ADCMEM_1_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_0_ADCMEM_1_REF_VOLTAGE_V                                       3.3
#define ADC12_0_ADCMEM_2                                      DL_ADC12_MEM_IDX_2
#define ADC12_0_ADCMEM_2_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_0_ADCMEM_2_REF_VOLTAGE_V                                       3.3
#define ADC12_0_ADCMEM_3                                      DL_ADC12_MEM_IDX_3
#define ADC12_0_ADCMEM_3_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_0_ADCMEM_3_REF_VOLTAGE_V                                       3.3
#define ADC12_0_ADCMEM_4                                      DL_ADC12_MEM_IDX_4
#define ADC12_0_ADCMEM_4_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_0_ADCMEM_4_REF_VOLTAGE_V                                       3.3
#define ADC12_0_ADCMEM_5                                      DL_ADC12_MEM_IDX_5
#define ADC12_0_ADCMEM_5_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_0_ADCMEM_5_REF_VOLTAGE_V                                       3.3
#define ADC12_0_ADCMEM_6                                      DL_ADC12_MEM_IDX_6
#define ADC12_0_ADCMEM_6_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_0_ADCMEM_6_REF_VOLTAGE_V                                       3.3
#define ADC12_0_ADCMEM_7                                      DL_ADC12_MEM_IDX_7
#define ADC12_0_ADCMEM_7_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_0_ADCMEM_7_REF_VOLTAGE_V                                       3.3
#define ADC12_0_ADCMEM_8                                      DL_ADC12_MEM_IDX_8
#define ADC12_0_ADCMEM_8_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_0_ADCMEM_8_REF_VOLTAGE_V                                       3.3
#define GPIO_ADC12_0_C1_PORT                                               GPIOA
#define GPIO_ADC12_0_C1_PIN                                       DL_GPIO_PIN_26
#define GPIO_ADC12_0_C2_PORT                                               GPIOA
#define GPIO_ADC12_0_C2_PIN                                       DL_GPIO_PIN_25
#define GPIO_ADC12_0_C3_PORT                                               GPIOA
#define GPIO_ADC12_0_C3_PIN                                       DL_GPIO_PIN_24
#define GPIO_ADC12_0_C4_PORT                                               GPIOB
#define GPIO_ADC12_0_C4_PIN                                       DL_GPIO_PIN_25
#define GPIO_ADC12_0_C5_PORT                                               GPIOB
#define GPIO_ADC12_0_C5_PIN                                       DL_GPIO_PIN_24
#define GPIO_ADC12_0_C6_PORT                                               GPIOB
#define GPIO_ADC12_0_C6_PIN                                       DL_GPIO_PIN_20
#define GPIO_ADC12_0_C7_PORT                                               GPIOA
#define GPIO_ADC12_0_C7_PIN                                       DL_GPIO_PIN_22
#define GPIO_ADC12_0_C8_PORT                                               GPIOA
#define GPIO_ADC12_0_C8_PIN                                       DL_GPIO_PIN_21

/* Defines for ADC12_1 */
#define ADC12_1_INST                                                        ADC1
#define ADC12_1_INST_IRQHandler                                  ADC1_IRQHandler
#define ADC12_1_INST_INT_IRQN                                    (ADC1_INT_IRQn)
#define ADC12_1_ADCMEM_0                                      DL_ADC12_MEM_IDX_0
#define ADC12_1_ADCMEM_0_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_1_ADCMEM_0_REF_VOLTAGE_V                                       3.3
#define ADC12_1_ADCMEM_1                                      DL_ADC12_MEM_IDX_1
#define ADC12_1_ADCMEM_1_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_1_ADCMEM_1_REF_VOLTAGE_V                                       3.3
#define ADC12_1_ADCMEM_2                                      DL_ADC12_MEM_IDX_2
#define ADC12_1_ADCMEM_2_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_1_ADCMEM_2_REF_VOLTAGE_V                                       3.3
#define GPIO_ADC12_1_C0_PORT                                               GPIOA
#define GPIO_ADC12_1_C0_PIN                                       DL_GPIO_PIN_15
#define GPIO_ADC12_1_C1_PORT                                               GPIOA
#define GPIO_ADC12_1_C1_PIN                                       DL_GPIO_PIN_16
#define GPIO_ADC12_1_C2_PORT                                               GPIOA
#define GPIO_ADC12_1_C2_PIN                                       DL_GPIO_PIN_17



/* Port definition for Pin Group GPIO_S1 */
#define GPIO_S1_PORT                                                     (GPIOA)

/* Defines for PIN_3: GPIOA.18 with pinCMx 40 on package pin 11 */
#define GPIO_S1_PIN_3_PIN                                       (DL_GPIO_PIN_18)
#define GPIO_S1_PIN_3_IOMUX                                      (IOMUX_PINCM40)
/* Port definition for Pin Group OLED */
#define OLED_PORT                                                        (GPIOB)

/* Defines for RST: GPIOB.17 with pinCMx 43 on package pin 14 */
#define OLED_RST_PIN                                            (DL_GPIO_PIN_17)
#define OLED_RST_IOMUX                                           (IOMUX_PINCM43)
/* Port definition for Pin Group FLASH */
#define FLASH_PORT                                                       (GPIOA)

/* Defines for FCS: GPIOA.1 with pinCMx 2 on package pin 34 */
#define FLASH_FCS_PIN                                            (DL_GPIO_PIN_1)
#define FLASH_FCS_IOMUX                                           (IOMUX_PINCM2)
/* Port definition for Pin Group BEEP */
#define BEEP_PORT                                                        (GPIOB)

/* Defines for PIN_0: GPIOB.0 with pinCMx 12 on package pin 47 */
#define BEEP_PIN_0_PIN                                           (DL_GPIO_PIN_0)
#define BEEP_PIN_0_IOMUX                                         (IOMUX_PINCM12)
/* Port definition for Pin Group ENCODE */
#define ENCODE_PORT                                                      (GPIOA)

/* Defines for DIR1: GPIOA.2 with pinCMx 7 on package pin 42 */
#define ENCODE_DIR1_PIN                                          (DL_GPIO_PIN_2)
#define ENCODE_DIR1_IOMUX                                         (IOMUX_PINCM7)
/* Defines for L0: GPIOA.0 with pinCMx 1 on package pin 33 */
#define LED_L0_PORT                                                      (GPIOA)
#define LED_L0_PIN                                               (DL_GPIO_PIN_0)
#define LED_L0_IOMUX                                              (IOMUX_PINCM1)
/* Defines for L1: GPIOB.22 with pinCMx 50 on package pin 21 */
#define LED_L1_PORT                                                      (GPIOB)
#define LED_L1_PIN                                              (DL_GPIO_PIN_22)
#define LED_L1_IOMUX                                             (IOMUX_PINCM50)
/* Defines for L2: GPIOB.26 with pinCMx 57 on package pin 28 */
#define LED_L2_PORT                                                      (GPIOB)
#define LED_L2_PIN                                              (DL_GPIO_PIN_26)
#define LED_L2_IOMUX                                             (IOMUX_PINCM57)
/* Port definition for Pin Group KEY */
#define KEY_PORT                                                         (GPIOB)

/* Defines for KU: GPIOB.8 with pinCMx 25 on package pin 60 */
#define KEY_KU_PIN                                               (DL_GPIO_PIN_8)
#define KEY_KU_IOMUX                                             (IOMUX_PINCM25)
/* Defines for KD: GPIOB.9 with pinCMx 26 on package pin 61 */
#define KEY_KD_PIN                                               (DL_GPIO_PIN_9)
#define KEY_KD_IOMUX                                             (IOMUX_PINCM26)
/* Defines for KR: GPIOB.10 with pinCMx 27 on package pin 62 */
#define KEY_KR_PIN                                              (DL_GPIO_PIN_10)
#define KEY_KR_IOMUX                                             (IOMUX_PINCM27)
/* Defines for KL: GPIOB.11 with pinCMx 28 on package pin 63 */
#define KEY_KL_PIN                                              (DL_GPIO_PIN_11)
#define KEY_KL_IOMUX                                             (IOMUX_PINCM28)
/* Defines for KM: GPIOB.21 with pinCMx 49 on package pin 20 */
#define KEY_KM_PIN                                              (DL_GPIO_PIN_21)
#define KEY_KM_IOMUX                                             (IOMUX_PINCM49)
/* Defines for TIRG1: GPIOB.12 with pinCMx 29 on package pin 64 */
#define ULN1_TIRG1_PORT                                                  (GPIOB)
#define ULN1_TIRG1_PIN                                          (DL_GPIO_PIN_12)
#define ULN1_TIRG1_IOMUX                                         (IOMUX_PINCM29)
/* Defines for ECHO1: GPIOA.31 with pinCMx 6 on package pin 39 */
#define ULN1_ECHO1_PORT                                                  (GPIOA)
#define ULN1_ECHO1_PIN                                          (DL_GPIO_PIN_31)
#define ULN1_ECHO1_IOMUX                                          (IOMUX_PINCM6)
/* Port definition for Pin Group ULN2 */
#define ULN2_PORT                                                        (GPIOB)

/* Defines for TIRG2: GPIOB.23 with pinCMx 51 on package pin 22 */
#define ULN2_TIRG2_PIN                                          (DL_GPIO_PIN_23)
#define ULN2_TIRG2_IOMUX                                         (IOMUX_PINCM51)
/* Defines for ECHO2: GPIOB.13 with pinCMx 30 on package pin 1 */
#define ULN2_ECHO2_PIN                                          (DL_GPIO_PIN_13)
#define ULN2_ECHO2_IOMUX                                         (IOMUX_PINCM30)
/* Defines for M1_DIR1: GPIOB.27 with pinCMx 58 on package pin 29 */
#define MOTOR_DIR_M1_DIR1_PORT                                           (GPIOB)
#define MOTOR_DIR_M1_DIR1_PIN                                   (DL_GPIO_PIN_27)
#define MOTOR_DIR_M1_DIR1_IOMUX                                  (IOMUX_PINCM58)
/* Defines for M1_DIR2: GPIOB.14 with pinCMx 31 on package pin 2 */
#define MOTOR_DIR_M1_DIR2_PORT                                           (GPIOB)
#define MOTOR_DIR_M1_DIR2_PIN                                   (DL_GPIO_PIN_14)
#define MOTOR_DIR_M1_DIR2_IOMUX                                  (IOMUX_PINCM31)
/* Defines for M2_DIR1: GPIOA.7 with pinCMx 14 on package pin 49 */
#define MOTOR_DIR_M2_DIR1_PORT                                           (GPIOA)
#define MOTOR_DIR_M2_DIR1_PIN                                    (DL_GPIO_PIN_7)
#define MOTOR_DIR_M2_DIR1_IOMUX                                  (IOMUX_PINCM14)
/* Defines for M2_DIR2: GPIOB.19 with pinCMx 45 on package pin 16 */
#define MOTOR_DIR_M2_DIR2_PORT                                           (GPIOB)
#define MOTOR_DIR_M2_DIR2_PIN                                   (DL_GPIO_PIN_19)
#define MOTOR_DIR_M2_DIR2_IOMUX                                  (IOMUX_PINCM45)

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_MOTOR_PWM_init(void);
void SYSCFG_DL_SERVO1_PWM_init(void);
void SYSCFG_DL_SERVO2_PWM_init(void);
void SYSCFG_DL_PWM_Cross_Trigger_init(void);
void SYSCFG_DL_QEI_0_init(void);
void SYSCFG_DL_CAPTURE_0_init(void);
void SYSCFG_DL_SYS_TIMER_init(void);
void SYSCFG_DL_I2C_0_init(void);
void SYSCFG_DL_HC05_UART_init(void);
void SYSCFG_DL_SENSER_UART_init(void);
void SYSCFG_DL_SERVO_UART_init(void);
void SYSCFG_DL_LCD_SPI_init(void);
void SYSCFG_DL_ADC12_0_init(void);
void SYSCFG_DL_ADC12_1_init(void);

bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
