#ifndef __KEY_H_
#define __KEY_H_

#include "ti_msp_dl_config.h"


//#define READ_S1					DL_GPIO_readPins(KEY_PORT, KEY_KU_PIN) 
//#define READ_S2					DL_GPIO_readPins(GPIO_S2_PORT, GPIO_S2_PIN_4_PIN) 

#define KEYU	DL_GPIO_readPins(KEY_PORT, KEY_KU_PIN) 
#define KEYD	DL_GPIO_readPins(KEY_PORT, KEY_KD_PIN) 
#define KEYR	DL_GPIO_readPins(KEY_PORT, KEY_KR_PIN) 
#define KEYL	DL_GPIO_readPins(KEY_PORT, KEY_KL_PIN) 
#define KEYM	DL_GPIO_readPins(KEY_PORT, KEY_KM_PIN) 

extern unsigned int KEYU_Times;
extern unsigned int KEYD_Times;
extern unsigned int KEYR_Times;
extern unsigned int KEYL_Times;
extern unsigned int KEYM_Times;


extern unsigned char KEYU_Flag;
extern unsigned char KEYD_Flag;
extern unsigned char KEYR_Flag;
extern unsigned char KEYL_Flag;
extern unsigned char KEYM_Flag;

void KEY_Init(void);
void Check_Key(void);
#endif

